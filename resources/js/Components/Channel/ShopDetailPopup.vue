<script setup lang="ts">
import ChannelForm from "@/Components/Channel/ChannelForm.vue";
import TheDetail from "@/Components/Channel/Shop/TheDetail.vue";
import UiModal from "@/Components/Ui/Modal";
import Protectedshop from "@/Components/Channel/Shop/Protectedshop.vue";
import { nextTick, ref } from "vue";
import { $http } from "@/lib/http";
import UiButton from "@/Components/Ui/Button";
import { createToaster } from "@/Components/Ui/Toaster";
import { useI18n } from "vue-i18n";
import { router, useForm } from "@inertiajs/vue3";
import type { InputField, DetailRow } from "@/Components/Channel/types";
import { useSwal } from "@/composables/useSwal";
import ClipboardButton from "@/Components/Ui/Button/ClipboardButton.vue";
import InstructionComponent from "@/Components/ProductChannel/Overview/Export/InstructionComponent.vue";
import type { ChannelKey } from "@/Components/Product/types";

const { t } = useI18n({});
const { createSwal } = useSwal();

const modalRef = ref<InstanceType<typeof UiModal>>();

const channelFormRef = ref<InstanceType<typeof ChannelForm>>();

const shopDetailId = ref<number>();

const toaster = createToaster();

const activeEditMode = ref(false);
const deprecated = ref(false);
const channelKey = ref<ChannelKey>();
const feedUrl = ref<string>();

const fields = ref<InputField[]>([]);
const rowData = ref<DetailRow>();

const loadForm = (shopId: number) => {
    shopDetailId.value = shopId;
    activeEditMode.value = false;
    fields.value = [];
    rowData.value = undefined;

    $http
        .get(`/products/channels/detail/${shopId}`)
        .then((r) => r.data)
        .then(async (res) => {
            fields.value = res.form;
            rowData.value = res.row;
            deprecated.value = Boolean(res.deprecated);
            channelKey.value = res.channelKey;
            feedUrl.value = res.feedUrl;
            modalRef.value?.show();
            await nextTick();
            channelFormRef.value?.setFields(res.form);
        })
        .catch((err) => {
            toaster.show(
                err.response?.data?.message || t("Something went wrong"),
                {
                    variant: "danger",
                    solid: false,
                },
            );
        });
};

const busy = ref(false);
const save = async () => {
    if (!shopDetailId.value) return;

    const isValid = await channelFormRef.value?.isValid();
    if (!isValid) return;

    busy.value = true;

    const data = channelFormRef.value?.getData();

    $http
        .put(`/products/channels/edit/${shopDetailId.value}`, data)
        .then((r) => r.data)
        .then((res) => {
            toaster.show(res.message, {
                variant: "success",
                solid: false,
            });
            modalRef.value?.hide();
            router.reload();
        })
        .catch((err) => {
            if (err.response?.data?.errors) {
                channelFormRef.value?.setErrors(err.response.data.errors);
            }

            toaster.show(
                err.response?.data?.message || t("Something went wrong"),
                {
                    variant: "danger",
                    solid: false,
                },
            );
        })
        .finally(() => {
            busy.value = false;
        });
};

const protectedShopRef = ref<InstanceType<typeof Protectedshop>>();
const protectedShopPopup = (doc: string) => {
    protectedShopRef.value?.show({
        title: doc.split("/").pop() as string,
        source: doc,
    });
    close();
};

const close = () => {
    modalRef.value?.hide();
};

const form = useForm({});

const deleteHandler = () => {
    if (!shopDetailId.value) return;

    const Swal = createSwal();
    Swal.fire({
        title: t("Are you sure"),
        text: t("Revert not possible"),
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: t("Confirm delete"),
    }).then((result) => {
        if (result.isConfirmed) {
            form.delete(`/products/channels/delete/${shopDetailId.value}`, {
                onSuccess: () => close(),
            });
        }
    });
};

defineExpose({ show: loadForm });
</script>

<template>
    <div>
        <UiModal
            ref="modalRef"
            title="Channel"
            backdrop="static"
            :hide-ok="!activeEditMode"
        >
            <TheDetail
                v-if="!activeEditMode && shopDetailId"
                :deprecated="deprecated"
                :fields="fields"
                :row="rowData"
                :shop-id="shopDetailId"
                :channel-key="channelKey"
                @edit="activeEditMode = true"
                @close="close"
                @protectedshop="protectedShopPopup"
            />
            <ChannelForm v-show="activeEditMode" ref="channelFormRef" />

            <div v-if="!activeEditMode && channelKey && feedUrl">
                <ClipboardButton
                    :value="feedUrl"
                    class="form-control bg-body-tertiary"
                    style="user-select: all"
                    >{{ feedUrl }}</ClipboardButton
                >
                <InstructionComponent :channel-key="channelKey" />
            </div>

            <template #ok-button>
                <UiButton
                    v-if="!activeEditMode"
                    class="btn btn-sm btn-danger"
                    @click="deleteHandler"
                    >{{ t("Delete") }}</UiButton
                >
                <UiButton
                    v-if="!deprecated && activeEditMode"
                    class="btn btn-sm btn-primary"
                    :busy="busy"
                    @click="save"
                    >{{ t("Save") }}</UiButton
                >
            </template>
        </UiModal>
        <Protectedshop ref="protectedShopRef" />
    </div>
</template>
