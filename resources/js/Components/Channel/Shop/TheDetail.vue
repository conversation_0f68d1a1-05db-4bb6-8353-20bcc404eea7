<script setup lang="ts">
import type { InputField, DetailRow } from "@/Components/Channel/types";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import UiPopover from "@/Components/Ui/Popover.vue";
import UiSpinner from "@/Components/Ui/Spinner";
import { createToaster } from "@/Components/Ui/Toaster";
import { useSwal } from "@/composables/useSwal";
import { usePage } from "@inertiajs/vue3";

/* import specific icons */
import {
    faDownload,
    faPencilAlt,
    faPlugCircleCheck,
    faQuestionCircle,
} from "@fortawesome/free-solid-svg-icons";

import { library } from "@fortawesome/fontawesome-svg-core";
import { amountFormat, localeString } from "@/lib/util";
import { $http } from "@/lib/http";
import { useI18n } from "vue-i18n";
import { ref } from "vue";
library.add(faPencilAlt, faDownload, faPlugCircleCheck, faQuestionCircle);

const props = defineProps<{
    shopId: number;
    fields: InputField[];
    row?: DetailRow;
    deprecated: boolean;
    channelKey?: string;
}>();

const emit = defineEmits(["close", "edit", "protectedshop"]);
const toaster = createToaster();
const { createSwal } = useSwal();
const { t } = useI18n({
    messages: {
        en: {
            Products: "Products",
            Version: "Version",
            Category: "Category",
            "Legal texts": "Legal texts",
            "Auto transfer": "Auto transfer",
            Turnover: "Turnover",
            "Do you want to enable auto transfer":
                "Do you want to enable auto transfer feature for this shop to transfer products automatically?",
        },
        de: {
            Products: "Produkte",
            Version: "Version",
            Category: "Kategorie",
            "Legal texts": "Rechtstexte",
            "Auto transfer": "Automatische Übertragung",
            Turnover: "Umsatz",
            "Do you want to enable auto transfer":
                "Möchten Sie die automatische Übertragungsfunktion für diesen Shop aktivieren, um Produkte automatisch zu übertragen?",
        },
        es: {
            Products: "Productos",
            Version: "Versión",
            Category: "Categoría",
            "Legal texts": "Textos jurídicos",
            "Auto transfer": "Transferencia automática",
            Turnover: "Facturación",
            "Do you want to enable auto transfer":
                "Quieres habilitar la función de transferencia automática para que esta tienda transfiera productos automáticamente?",
        },
    },
});
const autoTransfer = ref<boolean>(props?.row?.autoTransfer.active as boolean);

const updateAutoTransfer = (ev: Event) => {
    const checked = (ev.target as HTMLInputElement).checked;
    (ev.target as HTMLInputElement).checked = !checked;

    if (!props.shopId) return;

    if (checked) {
        _autoTransferActiveConfirmation();
    } else {
        _sendAutoTransferRequest(false);
    }
};

const _autoTransferActiveConfirmation = () => {
    const Swal = createSwal();
    Swal.fire({
        title: t("Hello"),
        text: t("Do you want to enable auto transfer"),
        icon: "info",
        showCancelButton: true,
        confirmButtonText: t("Yes"),
        customClass: {
            confirmButton: "btn btn-primary m-1",
            cancelButton: "btn btn-secondary m-1",
        },
        width: "25em",
    }).then((result) => {
        if (result.isConfirmed) {
            _sendAutoTransferRequest(true);
        }
    });
};

const busyAutoTransfer = ref(false);
const _sendAutoTransferRequest = (checked: boolean) => {
    busyAutoTransfer.value = true;
    $http
        .put(`/products/channels/update-auto-transfer/${props.shopId}`, {
            action: checked ? "on" : "off",
        })
        .then((r) => r.data)
        .then(async (res) => {
            autoTransfer.value = checked;
            toaster.show(res.message, {
                variant: "success",
                solid: false,
            });
        })
        .catch((err) => {
            toaster.show(
                err.response?.data?.message || t("Something went wrong"),
                {
                    variant: "danger",
                    solid: false,
                },
            );
        })
        .finally(() => {
            busyAutoTransfer.value = false;
        });
};

const EBAY_URL =
    "https://auth.ebay.com/oauth2/authorize?client_id=EXPERTIS-DRM-PRD-f193e18a1-6a51f647&response_type=code&redirect_uri=EXPERTISEROCKS_-EXPERTIS-DRM-PR-xszybym&scope=https://api.ebay.com/oauth/api_scope%20https://api.ebay.com/oauth/api_scope/sell.marketing.readonly%20https://api.ebay.com/oauth/api_scope/sell.marketing%20https://api.ebay.com/oauth/api_scope/sell.inventory.readonly%20https://api.ebay.com/oauth/api_scope/sell.inventory%20https://api.ebay.com/oauth/api_scope/sell.account.readonly%20https://api.ebay.com/oauth/api_scope/sell.account%20https://api.ebay.com/oauth/api_scope/sell.fulfillment.readonly%20https://api.ebay.com/oauth/api_scope/sell.fulfillment%20https://api.ebay.com/oauth/api_scope/sell.analytics.readonly%20https://api.ebay.com/oauth/api_scope/sell.finances%20https://api.ebay.com/oauth/api_scope/sell.payment.dispute%20https://api.ebay.com/oauth/api_scope/commerce.identity.readonly";

function openEbayReconnect() {
    // Build state object
    const page = usePage();
    const currentUserId = (page.props as any)?.auth?.user?.id || null;
    const stateObj = {
        user_id: currentUserId,
        action: 'update',
        timestamp: Math.floor(Date.now() / 1000),
        shop_id: props.shopId
    };
    const state = btoa(JSON.stringify(stateObj));
    const url = EBAY_URL + `&state=${encodeURIComponent(state)}`;
    window.open(url, '_blank');
}
</script>

<template>
    <div>
        <div v-if="fields && fields.length" class="card">
            <div v-if="!deprecated" class="position-absolute top-0 end-0 m-1">
                <button
                    class="btn btn-primary btn-sm"
                    style="
                        --bs-btn-padding-x: 0.35rem;
                        --bs-btn-padding-y: 0.15rem;
                    "
                    @click="emit('edit')"
                >
                    <FontAwesomeIcon icon="fa-solid fa-pencil-alt" />
                </button>
            </div>
            <div class="card-body">
                <div v-for="(field, idx) in fields" :key="field.name">
                    <b>{{ field.label }}</b>: {{ field.value }}
                    <!-- Place reconnect button after Password/Secret for eBay -->
                    <template v-if="channelKey === 'ebay' && (field.name.toLowerCase().includes('password') || field.name.toLowerCase().includes('secret'))">
                        <div class="mt-2 text-end">
                            <button
                                class="btn btn-warning btn-sm"
                                @click="openEbayReconnect"
                                type="button"
                            >
                                <FontAwesomeIcon icon="fa-solid fa-plug-circle-check" class="me-1" />
                                Reconnect eBay Shop
                            </button>
                        </div>
                    </template>
                </div>
            </div>
        </div>

        <div v-if="row" class="table-responsive mt-3">
            <table class="table table-bordered">
                <tbody>
                    <tr>
                        <th>{{ t("Products") }}</th>
                        <td>{{ localeString(row.productCount) }}</td>
                    </tr>
                    <tr>
                        <th>{{ t("Version") }}</th>
                        <td>{{ row.version }}</td>
                    </tr>
                    <tr>
                        <th>{{ t("Category") }}</th>
                        <td>{{ row.category }}</td>
                    </tr>
                    <tr>
                        <th>{{ t("Legal texts") }}</th>
                        <td>
                            <div
                                v-if="!!row?.protectedShops?.length"
                                class="dropdown position-static"
                            >
                                <button
                                    v-show="row.protectedShops?.length"
                                    class="btn btn-success btn-sm dropdown-toggle me-2"
                                    type="button"
                                    data-bs-toggle="dropdown"
                                    aria-expanded="false"
                                >
                                    <FontAwesomeIcon
                                        icon="fa-solid fa-download"
                                    />
                                </button>
                                <button
                                    class="btn btn-sm btn-primary"
                                    @click="
                                        emit(
                                            'protectedshop',
                                            `/products/channels/protected-shops/shop/${shopId}`,
                                        )
                                    "
                                >
                                    <FontAwesomeIcon
                                        icon="fa-solid fa-pencil-alt"
                                    />
                                    {{ $t("Edit") }}
                                </button>
                                <ul class="dropdown-menu max-h-350 scrollbar-y">
                                    <template
                                        v-for="(
                                            docs, docIndex
                                        ) in row.protectedShops"
                                        :key="docs.label"
                                    >
                                        <li
                                            v-for="doc in docs.value"
                                            :key="doc.label"
                                        >
                                            <a
                                                class="dropdown-item"
                                                target="_blank"
                                                :href="doc.value"
                                                ><FontAwesomeIcon
                                                    icon="fa-solid fa-download"
                                                    class="text-success"
                                                />
                                                {{ docs.label }} - ({{
                                                    doc.label
                                                }})</a
                                            >
                                        </li>
                                        <li
                                            v-show="
                                                docIndex !=
                                                row.protectedShops.length - 1
                                            "
                                        >
                                            <hr class="dropdown-divider" />
                                        </li>
                                    </template>
                                </ul>
                            </div>
                            <button
                                v-else-if="row.hasProtectedShopAvailable"
                                class="btn btn-sm btn-primary"
                                @click="
                                    emit(
                                        'protectedshop',
                                        `/products/channels/protected-shops/shop/${shopId}`,
                                    )
                                "
                            >
                                <FontAwesomeIcon
                                    icon="fa-solid fa-plug-circle-check"
                                />
                                {{ $t("Connect") }}
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <th>{{ t("Auto transfer") }}</th>
                        <td>
                            <UiSpinner v-if="busyAutoTransfer" size="sm" />
                            <div
                                v-else
                                class="form-check form-check-sm form-switch"
                            >
                                <input
                                    class="form-check-input"
                                    type="checkbox"
                                    role="switch"
                                    :disabled="row.autoTransfer.disabled"
                                    :checked="autoTransfer"
                                    @click="updateAutoTransfer"
                                />

                                <UiPopover
                                    v-if="
                                        !autoTransfer &&
                                        row.autoTransfer.disabled &&
                                        row.autoTransfer.message
                                    "
                                    :content="row.autoTransfer.message"
                                    ><FontAwesomeIcon
                                        icon="fa-solid fa-question-circle"
                                    />
                                </UiPopover>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <th>{{ t("Turnover") }}</th>
                        <td>{{ amountFormat(row.turnover) }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>
