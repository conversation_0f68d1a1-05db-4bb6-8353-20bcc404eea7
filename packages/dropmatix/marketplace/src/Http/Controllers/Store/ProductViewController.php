<?php

namespace Dropmatix\Marketplace\Http\Controllers\Store;

use App\Exceptions\DropmatixException;
use App\Http\Controllers\Controller;
use App\Models\Order\MpPaymentAgreement;
use App\Services\Tariff\ProductPlan;
use App\Services\User\CurrentUser;
use Dropmatix\Marketplace\Models\Product;
use Dropmatix\Marketplace\Services\ProductFormat;

class ProductViewController extends Controller
{
    private $countryInfo = [];

    public function __construct(
        private readonly CurrentUser $user,
        private readonly ProductPlan $tariff,
        private readonly MpCurrentCountry $currentCountry
    ) {}

    public function __invoke($product_id)
    {
        if (! request()->has('country')) {
            throw new DropmatixException('Country not found', 404);
        }

        $this->countryInfo = $this->currentCountry->getSessionCountryId();
        $product = Product::where('marketplace_products.id', $product_id)->forDetailView(request()->country)->first();

        if (blank($product)) {
            throw new DropmatixException(__('Invalid access'));
        }

        $markupPrice = MpPaymentAgreement::forActiveUser($this->user->parentId())->value('price_markup') ?? 0;

        $supperUser = $this->user->isMarketplaceAdmin();

        return response()->json([
            'success' => true,
            'data' => (new ProductFormat)->storePageDetailsFormat(
                $product,
                (float) $markupPrice,
                $this->countryInfo,
                $supperUser,
                $this->tariff->tariffId(),
                $this->user->parentId()
            ),
        ]);
    }
}
