<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\Shop\Shop;
use App\Models\Channel\ChannelProduct;
use App\Models\Product\Product;
use Exception;

class ApplyStockDistributionToShop extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:apply-stock-distribution-to-shop';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Apply stock distribution calculations directly to shop products using values from channel_stock_distributions table';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $userId = 3602;
        $shopId = 1072;
        $batchSize = 100;

        $this->info("Starting stock distribution application for user {$userId} to shop {$shopId}...");

        $shop = Shop::where([
            'id' => $shopId,
            'user_id' => $userId
        ])->first();

        if (!$shop) {
            $this->error("Shop with ID {$shopId} not found for user {$userId}.");
            return;
        }

        // Get all products that have stock distribution for this shop's channel
        $stockDistributions = DB::table('channel_stock_distributions')
            ->where('channel', $shop->channel)
            ->whereIn('drm_product_id', function ($query) use ($userId) {
                $query->select('id')
                    ->from('drm_products_new')
                    ->where('user_id', $userId);
            })
            ->get();

        if ($stockDistributions->isEmpty()) {
            $this->info('No stock distribution records found for this user and shop channel.');
            return;
        }

        $this->info("Found {$stockDistributions->count()} stock distribution records to process...");

        $processed = 0;
        $updated = 0;
        $errors = 0;

        foreach ($stockDistributions->chunk($batchSize) as $batch) {
            foreach ($batch as $distribution) {
                try {
                    // Get the main product stock
                    $mainProduct = Product::where('id', $distribution->drm_product_id)
                        ->where('user_id', $userId)
                        ->first();

                    if (!$mainProduct) {
                        $this->warn("Product ID {$distribution->drm_product_id} not found for user {$userId}.");
                        $errors++;
                        continue;
                    }

                    // Get the channel product for this shop
                    $channelProduct = ChannelProduct::where('drm_product_id', $distribution->drm_product_id)
                        ->where('shop_id', $shopId)
                        ->where('user_id', $userId)
                        ->first();

                    if (!$channelProduct) {
                        $this->warn("Channel product not found for product ID {$distribution->drm_product_id} in shop {$shopId}. Removing distribution entry.");
                        
                        // Remove the stock distribution entry since the shop product doesn't exist
                        DB::table('channel_stock_distributions')
                            ->where('drm_product_id', $distribution->drm_product_id)
                            ->where('channel', $shop->channel)
                            ->delete();
                        
                        $this->info("  ✓ Removed stock distribution entry for product {$distribution->drm_product_id}");
                        $errors++;
                        continue;
                    }

                    // Calculate the distributed stock
                    $mainStock = (float) ($mainProduct->stock ?? 0);
                    $percent = (float) ($distribution->percent ?? 0);

                    // Ensure percentage is within valid range (0-100)
                    $percent = max(0, min(100, $percent));

                    // Calculate distributed stock
                    $distributedStock = ($mainStock * $percent) / 100;
                    $finalStock = floor(max(0, $distributedStock));

                    $this->line("Product {$distribution->drm_product_id}: Main stock {$mainStock} × {$percent}% = {$finalStock}");

                    // Update the channel product stock directly
                    DB::table('channel_product_stock')
                        ->updateOrInsert(
                            ['channel_product_id' => $channelProduct->id],
                            [
                                'stock' => $finalStock,
                                'updated_at' => now(),
                            ]
                        );

                    $updated++;

                    $processed++;
                } catch (Exception $e) {
                    $this->error("Error processing product ID {$distribution->drm_product_id}: {$e->getMessage()}");
                    $errors++;
                }
            }
        }

        $this->info("\n✓ Stock distribution application completed!");
        $this->info("Products processed: {$processed}");
        $this->info("Products updated: {$updated}");
        $this->info("Errors: {$errors}");
    }
}
