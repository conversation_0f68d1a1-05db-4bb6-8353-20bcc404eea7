<?php

namespace App\Services\Product\Transfer;

use App\Enums\AppStore as AppStoreEnum;
use App\Exceptions\DropmatixException;
use App\Models\Channel\ProfitCalculation;
use App\Models\Product\Product;
use App\Models\Shop\Shop;
use App\Services\DRM\AppHelper;
use App\Services\Product\ProductHelper;
use App\Services\Product\Transfer\DTO\ProductTransferOptionDto;
use App\Services\Product\Transfer\DTO\ProfitCalculationTransferData;
use App\Services\ProductChannel\Helper\ChannelHelper;
use App\Services\User\CurrentUser;

class API
{
    /**
     * Transfer product
     *
     * @throws DropmatixException
     */
    public function transfer(int $userId, int $shopId, array $bulkQuery, array $fields, bool $replace = false, ?int $tariffId = null, ?float $stockSplit = null, array $additionalPayload = []): void
    {
        $shop = Shop::with('user')->where('user_id', $userId)->find($shopId, ['id', 'country_id', 'channel', 'user_id', 'products_synced_at']);
        if (empty($shop) | empty($shop->user)) {
            return;
        }

        $currentUser = new CurrentUser($shop->user);

        // Auto calculation
        $calculation = ProfitCalculation::where('user_id', $userId)
            ->where('auto_calculation', 1)
            ->select(
                'id',
                'uvp',
                'price_on_request',
                'uvp_profit_range',
                'default',
                'additional_charge',
                'round_scale',
                'dynamic_shipping_cost',
                'shipping_cost',
                'profit_percent',
            )
            ->first();

        $hasCheapestPriceAccess = ChannelHelper::channelHasCheapestPriceAccess($tariffId);
        $hasProductImportTemplateAccess = resolve(AppHelper::class)->hasAccess($userId, AppStoreEnum::PRODUCT_IMPORT_TEMPLATE);

        $transferPayload = new ProductTransferOptionDto(
            replace: $replace,
            hasCheapestPriceAccess: $hasCheapestPriceAccess,
            hasDistributionAppAccess: true,
            hasProductImportTemplateAccess: $hasProductImportTemplateAccess,
            splitStock: $stockSplit,
            calculation: ! blank($calculation) ? ProfitCalculationTransferData::from($calculation->toArray()) : null,
            vk_price: $additionalPayload['vk_price'] ?? null,
            priceDroptienda: $additionalPayload['price_droptienda'] ?? false,
        );

        $table = (new Product)->getTable();

        Product::where([
            $table.'.user_id' => $userId,
        ])
            ->queryFilter($currentUser->authQuery(), $bulkQuery)
            ->whereNull($table.'.deleted_at')
            ->when(ChannelHelper::isMpShopTransferNotAllowed($shop), function ($queryBuilder) {
                $queryBuilder->joinMarketplaceAttributeQuery()
                    ->whereNull('marketplace_attribute.marketplace_product_id');
            })
            ->select($table.'.id')
            ->orderBy($table.'.id', 'desc')
            ->chunk(100, function ($chunk) use ($shop, $fields, $transferPayload) {
                (new MainLevelToChannel)->__invoke($shop, $chunk->pluck('id')->toArray(), $fields, $transferPayload);
            });
    }
}
